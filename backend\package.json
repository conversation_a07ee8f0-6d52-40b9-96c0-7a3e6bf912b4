{
  "name": "zybra-sms-backend",
  "version": "1.0.0",
  "description": "Zybra SMS/USSD Backend Service for DeFi transactions",
  "main": "app.js",
  "scripts": {
    "start": "node app.js",
    "dev": "nodemon app.js",
<<<<<<< HEAD
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
=======
    "test": "node scripts/run-tests.js",
    "test:unit": "node scripts/run-tests.js unit",
    "test:integration": "node scripts/run-tests.js integration",
    "test:api": "node scripts/run-tests.js api",
    "test:coverage": "node scripts/run-tests.js coverage",
    "test:watch": "jest --watch",
    "test:jest": "jest",
>>>>>>> e493750ee6533facd8eb627b1ad0498cb277d1f1
    "db:migrate": "node scripts/migrate.js",
    "db:seed": "node scripts/seed.js",
    "lint": "eslint .",
    "lint:fix": "eslint . --fix",
    "format": "prettier --write .",
    "docker:build": "docker build -t zybra-sms-backend .",
    "docker:run": "docker run -p 3000:3000 zybra-sms-backend"
  },
  "keywords": [
    "sms",
    "ussd",
    "defi",
    "blockchain",
    "africastalking",
    "mobile-money",
    "zrusd"
  ],
  "author": "Zybra Team",
  "license": "MIT",
  "dependencies": {
<<<<<<< HEAD
=======
    "@morpho-org/blue-sdk": "^3.0.9",
    "@morpho-org/blue-sdk-ethers": "^3.0.0",
    "@morpho-org/morpho-ts": "^2.4.1",
>>>>>>> e493750ee6533facd8eb627b1ad0498cb277d1f1
    "africastalking": "^0.6.0",
    "axios": "^1.6.0",
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "ethers": "^6.8.0",
    "express": "^4.18.2",
    "express-rate-limit": "^7.1.5",
    "express-validator": "^7.0.1",
    "helmet": "^7.1.0",
    "jsonwebtoken": "^9.0.2",
    "morgan": "^1.10.0",
    "pg": "^8.11.3",
    "redis": "^4.6.10",
    "winston": "^3.11.0"
  },
  "devDependencies": {
    "@types/node": "^20.8.0",
    "eslint": "^8.52.0",
    "eslint-config-prettier": "^9.0.0",
    "eslint-plugin-node": "^11.1.0",
    "jest": "^29.7.0",
    "nodemon": "^3.0.1",
    "prettier": "^3.0.3",
    "supertest": "^6.3.3"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=8.0.0"
  },
  "repository": {
    "type": "git",
    "url": "https://github.com/zybra/sms-backend.git"
  },
  "bugs": {
    "url": "https://github.com/zybra/sms-backend/issues"
  },
  "homepage": "https://github.com/zybra/sms-backend#readme"
}
