version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: zybra-postgres
    environment:
      POSTGRES_DB: zybra_sms
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/db/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    networks:
      - zybra-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: zybra-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - zybra-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Zybra Backend Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: zybra-backend
    environment:
      NODE_ENV: production
      PORT: 3000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: zybra_sms
      DB_USER: postgres
      DB_PASSWORD: password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      # Add other environment variables from .env file
    ports:
      - "3000:3000"
    depends_on:
      - postgres
      - redis
    networks:
      - zybra-network
    restart: unless-stopped
    volumes:
      - ./backend/logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: zybra-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - backend
    networks:
      - zybra-network
    restart: unless-stopped

  # Local Ethereum Node (Optional - for development)
  ganache:
    image: trufflesuite/ganache:latest
    container_name: zybra-ganache
    ports:
      - "8545:8545"
    command: >
      --host 0.0.0.0
      --accounts 10
      --deterministic
      --mnemonic "your twelve word mnemonic phrase here for development only"
      --networkId 1337
      --gasLimit ********
    networks:
      - zybra-network
    restart: unless-stopped

  # Monitoring with Prometheus (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: zybra-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - zybra-network
    restart: unless-stopped

  # Grafana for Dashboards (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: zybra-grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - zybra-network
    restart: unless-stopped

networks:
  zybra-network:
    driver: bridge

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
