# Zybra SMS/USSD Backend Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=zybra_sms
DB_USER=postgres
DB_PASSWORD=password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Africa's Talking Configuration
AFRICASTALKING_USERNAME=your_username
AFRICASTALKING_API_KEY=your_api_key
AFRICASTALKING_SENDER_ID=ZYBRA

# Blockchain Configuration
RPC_URL=http://localhost:8545
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_infura_project_id
BASE_RPC_URL=https://mainnet.base.org
ZRUSD_CONTRACT_ADDRESS=******************************************
MASTER_PRIVATE_KEY=0x0000000000000000000000000000000000000000000000000000000000000000

# Security Configuration
JWT_SECRET=your_jwt_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# External API Configuration
CHAINLINK_API_URL=https://api.chain.link
YELLOWCARD_API_KEY=your_yellowcard_api_key
YELLOWCARD_API_SECRET=your_yellowcard_api_secret
YELLOWCARD_API_URL=https://sandbox.api.yellowcard.io
YELLOWCARD_WEBHOOK_SECRET=your_yellowcard_webhook_secret

# Kotani Pay Configuration
KOTANI_PAY_BASE_URL=https://sandbox-api.kotanipay.io/api/v3
KOTANI_PAY_API_KEY=your_kotani_pay_api_key
KOTANI_PAY_INTEGRATOR_ID=your_integrator_id
KOTANI_PAY_WEBHOOK_SECRET=your_webhook_secret

# Mobile Money Configuration
AIRTEL_API_KEY=your_airtel_api_key
AIRTEL_API_SECRET=your_airtel_api_secret
AIRTEL_BASE_URL=https://api.airtel.africa

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# SMS Configuration
SMS_RATE_LIMIT_PER_HOUR=10
OTP_EXPIRY_MINUTES=5

# USSD Configuration
USSD_SESSION_TIMEOUT_MINUTES=5
USSD_MAX_MENU_DEPTH=10

# Transaction Configuration
DAILY_TRANSACTION_LIMIT=10000
SINGLE_TRANSACTION_LIMIT=5000
TRANSACTION_FEE_PERCENTAGE=0.1

# Webhook Configuration
WEBHOOK_SECRET=your_webhook_secret
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090

# Development Configuration
DEBUG=zybra:*
MOCK_BLOCKCHAIN=false
MOCK_SMS=false
