version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: zybra-postgres-prod
    environment:
      POSTGRES_DB: ${DB_NAME:-zybra_sms}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./backend/src/db/schema.sql:/docker-entrypoint-initdb.d/schema.sql
      - ./backups:/backups
    networks:
      - zybra-prod-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: zybra-redis-prod
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_prod_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - zybra-prod-network
    restart: unless-stopped
    command: redis-server /usr/local/etc/redis/redis.conf --appendonly yes --requirepass ${REDIS_PASSWORD}
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Zybra Backend Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: zybra-backend-prod
    environment:
      NODE_ENV: production
      PORT: 3000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: ${DB_NAME:-zybra_sms}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      AFRICASTALKING_USERNAME: ${AFRICASTALKING_USERNAME}
      AFRICASTALKING_API_KEY: ${AFRICASTALKING_API_KEY}
      AFRICASTALKING_SENDER_ID: ${AFRICASTALKING_SENDER_ID:-ZYBRA}
      RPC_URL: ${RPC_URL}
      ZRUSD_CONTRACT_ADDRESS: ${ZRUSD_CONTRACT_ADDRESS}
      MASTER_PRIVATE_KEY: ${MASTER_PRIVATE_KEY}
      JWT_SECRET: ${JWT_SECRET}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY}
      YELLOWCARD_API_KEY: ${YELLOWCARD_API_KEY}
      AIRTEL_API_KEY: ${AIRTEL_API_KEY}
      WEBHOOK_SECRET: ${WEBHOOK_SECRET}
    ports:
      - "${PORT:-3000}:3000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - zybra-prod-network
    restart: unless-stopped
    volumes:
      - ./backend/logs:/app/logs
      - /etc/ssl/certs:/etc/ssl/certs:ro
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
      replicas: 2
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy & Load Balancer
  nginx:
    image: nginx:alpine
    container_name: zybra-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - backend
    networks:
      - zybra-prod-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: zybra-prometheus-prod
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.prod.yml:/etc/prometheus/prometheus.yml
      - prometheus_prod_data:/prometheus
    networks:
      - zybra-prod-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Grafana for Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: zybra-grafana-prod
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD}
      GF_SECURITY_SECRET_KEY: ${GRAFANA_SECRET_KEY}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_prod_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - zybra-prod-network
    restart: unless-stopped
    depends_on:
      - prometheus
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Log aggregation with Fluentd
  fluentd:
    build:
      context: ./logging
      dockerfile: Dockerfile
    container_name: zybra-fluentd-prod
    volumes:
      - ./logging/fluent.conf:/fluentd/etc/fluent.conf
      - ./backend/logs:/var/log/backend
      - ./nginx/logs:/var/log/nginx
    networks:
      - zybra-prod-network
    restart: unless-stopped
    depends_on:
      - backend
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # Backup service
  backup:
    image: postgres:15-alpine
    container_name: zybra-backup-prod
    environment:
      PGPASSWORD: ${DB_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh
    networks:
      - zybra-prod-network
    restart: "no"
    depends_on:
      - postgres
    command: /bin/sh -c "chmod +x /backup.sh && crond -f"

networks:
  zybra-prod-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/zybra/postgres
  redis_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/zybra/redis
  prometheus_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/zybra/prometheus
  grafana_prod_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/zybra/grafana
